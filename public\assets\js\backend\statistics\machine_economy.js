define(['jquery', 'bootstrap', 'backend', 'table', 'form'], function ($, undefined, Backend, Table, Form) {

    var Controller = {
        index: function () {
            // 初始化表格参数配置
            Table.api.init({
                extend: {
                    index_url: 'statistics/machine_economy/index',
                    add_url: '',
                    edit_url: '',
                    del_url: '',
                    multi_url: '',
                    table: 'machine_economy',
                }
            });

            var table = $("#table");

            //搜索框自定义
            table.on('post-common-search.bs.table', function (event, table) {
                var form = $("form", table.$commonsearch);

                $("input[name='c_id']", form).addClass("selectpage").data("source", "game/category/select_list").data("primaryKey", "id").data("field", "name").data("pageSize", "15").data("orderBy", "id desc").data("multiple","true");

                Form.events.cxselect(form);
                Form.events.selectpage(form);
            });

            //当表格数据加载完成时
            table.on('load-success.bs.table', function (e, data) {
                //这里可以获取从服务端获取的JSON数据

                //这里我们手动设置底部的值
                $("#used_coins").text(data.extend.used_coins);
                $("#won_coins").text(data.extend.won_coins);
                $("#profit").text(data.extend.profit);
            });

            // 初始化表格
            table.bootstrapTable({
                url: $.fn.bootstrapTable.defaults.extend.index_url,
                escape: false,
                pk: 'id',
                sortName: 'id',
                pageSize:10,
                search:false,
                columns: [
                    [
                        {checkbox: true},
                        {field: 'ranking', title: __('记录ID'), operate:false},
                        {field: 'game_id', title: __('房间ID'), operate:false},
                        {field: 'name', title: __('房间名'), operate:false},
                        {field: 'c_id', title: __('分类'),visible:false,commonSearch: false},
                        {field: 'c_name', title: __('分类'),operate: false},
                        {field: 'used_time', title: __('游玩时间(分钟)'), operate:false},
                        {field: 'used_coins', title: __('投币'), operate:false},
                        {field: 'won_coins', title: __('彩票'), operate:false},
                        {field: 'profit', title: __('收益'), operate:false},
                        {field: 'date', title: __('日期'), operate:false},
                        {field: 'end_time', title: __('日期'),visible:false, operate:'RANGE', sortable: true, addclass:'datetimerange',formatter: Table.api.formatter.datetime},
                        {field: 'operate', title: __('Operate'), table: table, events: Table.api.events.operate,
                            buttons:[
                                {
                                    name: 'detail',
                                    text: __('游戏记录'),
                                    icon: 'fa fa-list',
                                    extend:'data-area=\'["80%","60%"]\'',
                                    classname: 'btn btn-info btn-xs btn-detail btn-dialog',
                                    url: function (row){
                                        return "game/log/index?game_id=" + row.game_id+'&end_time='+row.date;
                                    }
                                },

                            ],
                            formatter: Table.api.formatter.operate
                        }
                    ]
                ]
            });

            // 为表格绑定事件
            Table.api.bindevent(table);

            //当表格数据加载完成时
            table.on('load-success.bs.table', function (e, data) {
                //修改编辑弹出窗口大小
                // $(".btn-editone").data("area", ["50%","80%"]);
            });
        },
        add: function () {
            Controller.api.bindevent();
        },
        edit: function () {
            Controller.api.bindevent();
        },
        end_game: function () {
            Controller.api.bindevent();
        },
        api: {
            bindevent: function () {
                Form.api.bindevent($("form[role=form]"));
            }
        }
    };
    return Controller;
});