#!/bin/bash

# 截图上传接口 CURL 测试脚本
# 测试域名: http://testva2.91jdcd.com
# 接口地址: /api/common/upload_screenshot

API_URL="http://testva2.91jdcd.com/api/common/upload_screenshot"
TEST_IMAGE="test_image.jpg"

echo "=== 截图上传接口 CURL 测试 ==="
echo "测试域名: http://testva2.91jdcd.com"
echo "接口地址: /api/common/upload_screenshot"
echo "测试时间: $(date '+%Y-%m-%d %H:%M:%S')"
echo ""

# 创建测试图片（使用ImageMagick或者下载一个测试图片）
if [ ! -f "$TEST_IMAGE" ]; then
    echo "创建测试图片..."
    # 如果系统有ImageMagick
    if command -v convert &> /dev/null; then
        convert -size 200x150 xc:lightgray -pointsize 20 -draw "text 50,75 'Test Image'" "$TEST_IMAGE"
        echo "✅ 测试图片已创建: $TEST_IMAGE"
    else
        echo "❌ 请手动创建测试图片文件: $TEST_IMAGE"
        echo "或者下载一个测试图片并重命名为: $TEST_IMAGE"
        exit 1
    fi
    echo ""
fi

# 检查文件是否存在
if [ ! -f "$TEST_IMAGE" ]; then
    echo "❌ 测试图片不存在: $TEST_IMAGE"
    exit 1
fi

# 获取文件信息
FILE_SIZE=$(stat -f%z "$TEST_IMAGE" 2>/dev/null || stat -c%s "$TEST_IMAGE" 2>/dev/null)
echo "文件信息:"
echo "- 文件名: $TEST_IMAGE"
echo "- 文件大小: $FILE_SIZE bytes ($(echo "scale=2; $FILE_SIZE/1024/1024" | bc)MB)"
echo ""

# 测试用例1: 正常上传（带分类参数）
echo "=== 测试用例1: 正常上传（带分类参数） ==="
echo "curl命令:"
echo "curl -X POST \\"
echo "  -F 'file=@$TEST_IMAGE' \\"
echo "  -F 'category=screenshot' \\"
echo "  '$API_URL'"
echo ""
echo "执行结果:"

curl -X POST \
  -F "file=@$TEST_IMAGE" \
  -F "category=screenshot" \
  -w "\n\nHTTP状态码: %{http_code}\n请求耗时: %{time_total}s\n" \
  "$API_URL"

echo ""
echo "----------------------------------------"
echo ""

# 测试用例2: 不带分类参数
echo "=== 测试用例2: 不带分类参数 ==="
echo "curl命令:"
echo "curl -X POST \\"
echo "  -F 'file=@$TEST_IMAGE' \\"
echo "  '$API_URL'"
echo ""
echo "执行结果:"

curl -X POST \
  -F "file=@$TEST_IMAGE" \
  -w "\n\nHTTP状态码: %{http_code}\n请求耗时: %{time_total}s\n" \
  "$API_URL"

echo ""
echo "----------------------------------------"
echo ""

# 测试用例3: 自定义分类
echo "=== 测试用例3: 自定义分类 ==="
echo "curl命令:"
echo "curl -X POST \\"
echo "  -F 'file=@$TEST_IMAGE' \\"
echo "  -F 'category=test_upload' \\"
echo "  '$API_URL'"
echo ""
echo "执行结果:"

curl -X POST \
  -F "file=@$TEST_IMAGE" \
  -F "category=test_upload" \
  -w "\n\nHTTP状态码: %{http_code}\n请求耗时: %{time_total}s\n" \
  "$API_URL"

echo ""
echo "----------------------------------------"
echo ""

# 测试用例4: 详细响应信息
echo "=== 测试用例4: 详细响应信息 ==="
echo "curl命令（包含详细信息）:"
echo "curl -X POST \\"
echo "  -F 'file=@$TEST_IMAGE' \\"
echo "  -F 'category=detailed_test' \\"
echo "  -v \\"
echo "  '$API_URL'"
echo ""
echo "执行结果:"

curl -X POST \
  -F "file=@$TEST_IMAGE" \
  -F "category=detailed_test" \
  -v \
  "$API_URL"

echo ""
echo ""
echo "=== 测试完成 ==="
echo ""
echo "预期结果说明:"
echo "1. HTTP状态码应该是 200"
echo "2. 响应JSON格式: {\"code\":1,\"msg\":\"截图上传成功\",\"data\":{\"url\":\"...\",\"fullurl\":\"...\"}}"
echo "3. 文件名格式应该是: YYYYMMDDHHMMSS_随机字符串.jpg"
echo "4. 文件路径格式应该是: /screenshot/YYYY/MM/文件名"
echo ""
echo "如果测试失败，请检查:"
echo "- 域名是否可访问: http://testva2.91jdcd.com"
echo "- 服务器是否正常运行"
echo "- 接口路径是否正确: /api/common/upload_screenshot"
echo "- 文件权限是否正确"
echo "- 服务器磁盘空间是否充足"
