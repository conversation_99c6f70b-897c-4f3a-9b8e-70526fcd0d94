<?php

namespace app\admin\controller\statistics;

use app\common\controller\Backend;
use think\Db;
use think\Exception;

/**
 * 机器产出总览
 * Class Order
 * @package app\admin\controller\order
 */
class MachineEconomy extends Backend
{
    /**
     * Withdraw模型对象
     * @var \app\admin\model\Agent
     */
    protected $model = null;
    protected $order_model = '';

    protected $noNeedRight = '';
    protected $multiFields = "deleted,rent_limit";
    public function _initialize()
    {
        $this->noNeedRight = array('index','coupon','store_coupon','recharge','order');

        parent::_initialize();
        $this->model = model('game');

        $this->view->assign("cateGoryList", $this->model->getCateGoryList());
    }

    /**
     * 总消费排行
     * @Authod Jw
     * @Time 2024/7/23
     * @return string|\think\response\Json
     * @throws Exception
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    public function index()
    {
        //设置过滤方法
        $this->request->filter(['strip_tags']);

        if ($this->request->isAjax()) {
            //如果发送的来源是Selectpage，则转发到Selectpage
            if ($this->request->request('keyField')) {
                return $this->selectpage();
            }

            //搜索提交过来的数据
            $search_arr = json_decode($this->request->request('filter'),1);
            $where_arr = [];

            $join_condition = 'g.id = l.game_id AND l.status = 3'; // 基本的JOIN条件

            //默认显示今天
            $time_s = date('Y-m-d 00:00:00',time());
            $time_e = date('Y-m-d 23:59:59',time());
            $date = $time_s.' - '.$time_e;

            $time_s = strtotime($time_s);
            $time_e = strtotime($time_e);
            // 将时间条件添加到JOIN条件中
            $join_condition .= " AND l.end_time BETWEEN {$time_s} AND {$time_e}";

            if ($search_arr) {
                foreach ($search_arr as $k => $v) {
                    if ($k == 'end_time'){
                        $date = $v;
                        $create_time = explode(' - ',$v);
                        // 将时间条件添加到JOIN条件中
                        $join_condition .= " AND l.end_time BETWEEN ".strtotime($create_time[0])." AND ".strtotime($create_time[1]);
                    }else{
                        $key = 'g.'.$k;
                        $where_arr[$key] = $v;
                    }
                    unset($search_arr[$k]);
                }
            }

            $this->request->get(['filter'=>json_encode($search_arr)]);
            list($where, $sort, $order, $offset, $limit) = $this->buildparams();

            $list = Db::name('game')
                ->alias('g')
                ->join(['fa_game_log l'], $join_condition, 'left') // 使用修改后的JOIN条件
                ->join(['fa_game_category c'],'g.c_id=c.id','left')
                ->where($where)
                ->where($where_arr)
                ->where('g.is_delete',0)
                ->field([
                    'g.id as game_id',
                    'g.name',
                    'c.name as c_name',
                    'IFNULL(SUM(l.used_time), 0) as used_time',
                    'IFNULL(SUM(l.used_coins), 0) as used_coins',
                    'IFNULL(SUM(l.won_coins), 0) as won_coins'
                ])
                ->group('g.id')
                ->order('g.id desc')
                ->paginate($limit);

            $total = $list->total();
            $list = $list->all(); //这是关键
            
            foreach ($list as $k => &$item)
            {
                $offset++;
                $item['ranking'] =$offset;
                $item['profit'] = $item['used_coins'] - $item['won_coins'];//收益 = 投币 - 彩票
                $item['used_time'] = ceil($item['used_time']/60);
                $item['date'] = $date;
            }
            unset($item);

            $stat = Db::name('game')
                ->alias('g')
                ->join(['fa_game_log l'], $join_condition, 'left') // 使用修改后的JOIN条件
                ->join(['fa_game_category c'],'g.c_id=c.id','left')
                ->where($where)
                ->where($where_arr)
                ->where('g.is_delete',0)
                ->field([
                    'IFNULL(SUM(l.used_coins), 0) as used_coins',
                    'IFNULL(SUM(l.won_coins), 0) as won_coins'
                ])
                ->find();
            $used_coins = $stat['used_coins'] ?? 0;//投币数量
            $won_coins = $stat['won_coins'] ?? 0;//用户获得的彩票
            $profit = $used_coins-$won_coins;//利润

            $result = array("total" => $total, "rows" => $list, "extend" => ['used_coins'=>$used_coins,'won_coins' => $won_coins,'profit'=>$profit]);

            return json($result);
        }
        return $this->view->fetch();
    }



}
