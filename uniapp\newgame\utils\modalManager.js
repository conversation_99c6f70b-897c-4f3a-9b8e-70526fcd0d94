// 全局弹窗管理器
class ModalManager {
  constructor() {
    this.currentPages = []
    this.modalQueue = []
    this.globalLoginModal = null // 全局登录弹窗实例
  }

  /**
   * 注册全局登录弹窗实例
   * @param {Object} instance 登录弹窗组件实例
   */
  registerGlobalLoginModal(instance) {
    this.globalLoginModal = instance
  }

  /**
   * 显示登录确认弹窗
   * @param {string} action 操作名称
   * @param {Function} onConfirm 确认回调
   * @param {Function} onCancel 取消回调
   */
  showLoginModal(action = '此操作', onConfirm = null, onCancel = null) {
    // 优先使用全局登录弹窗
    if (this.globalLoginModal) {
      this.globalLoginModal.show(action, onConfirm, onCancel)
      return
    }

    // 回退到页面级弹窗
    const pages = getCurrentPages()
    if (pages.length === 0) {
      console.warn('ModalManager: 无法获取当前页面')
      return
    }

    const currentPage = pages[pages.length - 1]
    
    // 检查当前页面是否有showLoginModal方法
    if (typeof currentPage.showLoginModal === 'function') {
      currentPage.showLoginModal(action, onConfirm, onCancel)
    } else {
      // 最后回退到原生弹窗
      this.showNativeLoginModal(action, onConfirm, onCancel)
    }
  }

  /**
   * 显示原生登录弹窗（作为备选方案）
   * @param {string} action 操作名称
   * @param {Function} onConfirm 确认回调
   * @param {Function} onCancel 取消回调
   */
  showNativeLoginModal(action = '此操作', onConfirm = null, onCancel = null) {
    uni.showModal({
      title: '登录提示',
      content: `${action}需要登录后才能使用，是否前往登录？`,
      confirmText: '去登录',
      cancelText: '取消',
      success: (res) => {
        if (res.confirm) {
          if (onConfirm) {
            onConfirm()
          } else {
            uni.navigateTo({
              url: '/pages/login/index'
            })
          }
        } else if (res.cancel && onCancel) {
          onCancel()
        }
      }
    })
  }

  /**
   * 注册页面的弹窗方法
   * @param {Object} pageInstance 页面实例
   */
  registerPage(pageInstance) {
    // 可以在这里注册页面实例，用于更复杂的管理
    console.log('ModalManager: 页面已注册', pageInstance)
  }
}

// 创建全局实例
const modalManager = new ModalManager()

export default modalManager