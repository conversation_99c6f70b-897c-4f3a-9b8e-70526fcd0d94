<style type="text/css">
    body {
        font-family: Source <PERSON> SC;
    }

    .row {
        margin: 0;
    }

    .header-con {
        padding: 0px 2.0% 20px 0px;
        float: left;
    }

    .header-conBox {
        background: #fff;
        padding: 16px 14px;
        border-radius: 14px;
        position: relative;
        height: 120px;
        border: 2px solid;
    }

    .header-con-tip {
        display: flex;
        align-items: flex-start;
        justify-content: space-between;
    }

    .header-con-tip-left {
        flex: 1;
        display: flex;
        align-items: center;
        font-size: 14px;
        font-weight: 500;
    }

    .header-con-unit {
        font-size: 12px;
    }

    .header-con-msg {
        padding-top: 10px;
        width: 100%;
    }

    .header-con-msg-num {
        font-size: 22px;
        font-weight: 600;
        line-height: 30px;
        text-align: center;
    }
    .main {
        border-radius: 20px;
        margin-bottom: 10px;
    }
    #antv-con {
        background: #f1f4f6;
    }

    .el-table td {
        height: 48px;
        padding: 0 !important;
    }

    .el-table th {
        height: 60px;
        padding: 0 !important;
    }

    .header-con-tip-left-1 {
        display: flex;
        align-items: center;
        justify-content: space-between;
        flex: 1;
    }

    [v-cloak] {
        display: none
    }
    #cxselect-example {
        background: #FFFFFF;
    }
    .el-col-xl-12 {
        width: 25% !important;
    }
    .sm-st-icon {
        width: 50px;
        height: 50px;
        display: inline-block;
        line-height: 50px;
        text-align: center;
        font-size: 30px;
        background: #eee;
        -webkit-border-radius: 5px;
        -moz-border-radius: 5px;
        border-radius: 5px;
        float: left;
        margin-right: 10px;
        color: #fff;
    }
</style>
<!--<link rel="stylesheet" href="/assets/addons/shopro/libs/element/element.css">-->
<!--<script type="text/javascript" src="https://fastly.jsdelivr.net/npm/echarts@5.3.3/dist/echarts.min.js"></script>-->
<div id="antv-con" class="">
    <!--start搜索-->
    <div class="row" id="cxselect-example"  style="margin-bottom:20px;padding: 10px;">
        <form id="form1" action="" role="form" novalidate class="form-inline">

            <a href="javascript:;" class="btn btn-primary btn-refresh"><i class="fa fa-refresh"></i></a>
            <a href="javascript:;" class="btn btn-success btn-filter">{:__('Today')}</a>
            <a href="javascript:;" class="btn btn-success btn-filter">{:__('Yesterday')}</a>
            <a href="javascript:;" class="btn btn-success btn-filter">{:__('Last 7 Days')}</a>
            <a href="javascript:;" class="btn btn-success btn-filter">{:__('Last 30 Days')}</a>
            <a href="javascript:;" class="btn btn-success btn-filter">{:__('Last month')}</a>
            <a href="javascript:;" class="btn btn-success btn-filter">{:__('This month')}</a>
            <div class="input-group">
                <span class="input-group-addon"><i class="fa fa-calendar"></i></span>
                <input type="text" class="form-control input-inline datetimerange" data-type="archives" placeholder="指定日期" style="width:280px;"/>
            </div>

        </form>
    </div>
    <!--end搜索-->

    <div class="row main el-row">
        <div class="el-col el-col-24 el-col-xs-24 el-col-sm-24 el-col-md-24 "  >
            <div class="el-row">
                {volist name="row" id="vo" mod="2" key="k"}

                        <div class=" header-con el-col el-col-24 el-col-xs-24 el-col-sm-12 el-col-md-12 el-col-lg-12 el-col-xl-12 ">
                            <div class="header-conBox header-conBox_1" data-type="1" data-name="{$vo.title}" data-title="" style="border-color: #66AAFF; color: #222D32">
                                <div class="header-con-tip">
                                    <div class="header-con-tip-left">
                                        <span class="sm-st-icon" style="background: #66AAFF"><i class="{$vo['coin']}"></i></span>
                                        <div style="flex: 1 1 0%;">
                                            <div class="header-con-tip-left-1">
                                                <span>{$vo.title}</span>
                                            </div>
                                            <div class="header-con-unit">{$vo.sub_title}</div>
                                        </div>
                                    </div>
                                </div>
                                <div class="header-con-msg">
                                    <div class="header-con-msg-num order_num">{$vo.value}</div>
                                </div>
                            </div>
                        </div>

                {/volist}
            </div>
        </div>
    </div>


</div>
