<?php

namespace app\admin\controller\agent;

use app\common\controller\Backend;
use app\common\model\MoneyLog;
use app\common\model\User;
use fast\Random;
use think\Db;
use think\Exception;
use think\exception\DbException;
use think\exception\PDOException;
use think\exception\ValidateException;
use think\Validate;

/**
 * 佣金记录
 *
 * @icon fa fa-user
 */
class Commission extends Backend
{
    protected $searchFields = 'username,nickname,mobile';

    public function _initialize()
    {
        $this->noNeedRight = array('select_list');

        parent::_initialize();
    }

    /**
     * 查看
     */
    public function index()
    {
        //设置过滤方法
        $this->request->filter(['strip_tags']);
        if ($this->request->isAjax()) {

            //如果发送的来源是Selectpage，则转发到Selectpage
            if ($this->request->request('keyField')) {
                return $this->selectpage();
            }

            //搜索提交过来的数据
            $search_arr = json_decode($this->request->request('filter'),1);
            $where_arr = [];

            $admin = get_admin_info();
            $is_show = 1;
            if ($admin['group_id'] == get_agent_group_id()) {//组别6=代理
                //代理只查看自己的
                $where_arr['l.agent_id'] = $admin['admin_id'];
                $is_show = 0;
            }

            if ($search_arr) {
                foreach ($search_arr as $k => $v) {
                    //执行时间
                    if ($k == 'createtime'){//创建时间
                        $create_time = explode(' - ',$v);
                        $where_arr['l.createtime'] = ['between',[strtotime($create_time[0]),strtotime($create_time[1])]];
                    } elseif ($k == 'order_no') {
                        $where_arr['l.correlation_id'] = Db::name('user_recharge_log')->where('order_no',$v)->value('id');
                    } else {
                        $str = 'l.'.$k;
                        $where_arr[$str] = $v;
                    }

                    unset($search_arr[$k]);
                }
            }

            $this->request->get(['filter'=>json_encode($search_arr)]);
            list($where, $sort, $order, $offset, $limit) = $this->buildparams();

            $total = Db::name('admin_commission_log')
                ->alias('l')
                ->where($where)
                ->where($where_arr)
                ->count();

            $list = Db::name('admin_commission_log')
                ->alias('l')
                ->join('admin a','l.agent_id = a.id','left')
                ->join('user u','l.user_id = u.id','left')
                ->where('a.is_agent',1)
                ->where($where)
                ->where($where_arr)
                ->order($sort, $order)
                ->field('l.*,a.nickname,a.mobile,u.nickname as user_nickname')
                ->limit($offset, $limit)
                ->select();

            $stat = Db::name('admin_commission_log')
                ->alias('l')
                ->join('admin a','l.agent_id = a.id','left')
                ->join('user u','l.user_id = u.id','left')
                ->where($where)
                ->where($where_arr)
                ->field([
                    'COALESCE(sum(if(l.status=1,l.commission,0)),0) as commission',
                ])
                ->find();
            $commission = $stat['commission'] ?? 0;//收入

            $result = array("total" => $total, "rows" => $list, "extend" => ['commission'=>$commission]);

            return json($result);
        }
        return $this->view->fetch();
    }



}