<?php

namespace app\admin\controller\statistics;

use app\common\controller\Backend;
use think\Db;

/**
 * 基础统计
 * @Authod Jw
 * @Time 2021/6/9
 * @package app\admin\controller\count
 */
class Index extends Backend
{
    public function _initialize()
    {
        $this->noNeedRight = ['cli'];
        $this->noNeedLogin = ['cli'];

        parent::_initialize();
    }

    /**
     * 查看
     */
    public function index()
    {
        if ($this->request->isPost()) {
            $date = $this->request->post('date', '');

            $statistics = $this->getStatisticsData($date);

            $this->success('', '', $statistics);
        }
        $statistics = $this->getStatisticsData();

        $this->view->assign('row', $statistics);
        return $this->view->fetch();
    }

    /**
     * 获取收支统计数据
     * @param string $date
     * @return array
     */
    protected function getStatisticsData($date = '')
    {
        if ($date) {
            $date = explode(" - ",$date);
        } else { // 默认显示今天
            $date = date('Y-m-d 00:00:00',time()).' - '.date('Y-m-d 23:59:59',time());
            $date = explode(" - ",$date);
        }
        $where = [];
        $time_s = strtotime($date[0]);
        $time_e = strtotime($date[1]);

        $where['pay_time'] = array('between',"{$time_s},{$time_e}");
        $userWhere['createtime'] = array('between',"{$time_s},{$time_e}");

        // 支付统计查询
        $paymentData = Db::name('payment')
            ->field([
                "SUM(amount) AS total_amount",
                "COUNT(*) AS total_orders",
                "COUNT(DISTINCT user_id) AS pay_users",
                "COUNT(DISTINCT CASE WHEN status=1 THEN user_id END) AS new_pay_users"
            ])
            ->where('status', 1) // 只统计支付成功的
            ->where($where)
            ->cache(60)
            ->find();

        $row[] = [
            'title'=>'支付总额',
            'sub_title'=>'单位/元',
            'coin'=>'fa fa-align-right',
            'value'=>number_format($paymentData['total_amount'] ?? 0, 2)
        ];
        $row[] = [
            'title'=>'支付订单数',
            'sub_title'=>'单位/笔',
            'coin'=>'fa fa-align-right',
            'value'=>$paymentData['total_orders'] ?? 0
        ];
        $row[] = [
            'title'=>'消费人数',
            'sub_title'=>'',
            'coin'=>'fa fa-align-right',
            'value'=>$paymentData['pay_users'] ?? 0
        ];
        $row[] = [
            'title'=>'新增付费用户',
            'sub_title'=>'',
            'coin'=>'fa fa-align-right',
            'value'=>$paymentData['new_pay_users'] ?? 0
        ];

        $newUser = Db::name('user')
            ->where($userWhere)
            ->cache(60)
            ->count();
        $row[] = [
            'title'=>'新增用户',
            'sub_title'=>'',
            'coin'=>'fa fa-align-right',
            'value'=>$newUser,
        ];

        return $row;
    }


}
