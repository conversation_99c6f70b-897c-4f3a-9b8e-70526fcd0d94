<?php

namespace app\admin\controller\statistics;

use app\common\controller\Backend;
use think\Db;
use think\Exception;

/**
 * 用户排行
 * Class Order
 * @package app\admin\controller\order
 */
class User extends Backend
{
    /**
     * Withdraw模型对象
     * @var \app\admin\model\Agent
     */
    protected $model = null;
    protected $order_model = '';

    protected $noNeedRight = '';
    protected $multiFields = "deleted,rent_limit";
    public function _initialize()
    {
        $this->noNeedRight = array('index','coupon','store_coupon','recharge','order');

        parent::_initialize();
    }

    /**
     * 总消费排行
     * @Authod Jw
     * @Time 2024/7/23
     * @return string|\think\response\Json
     * @throws Exception
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    public function index()
    {
        //设置过滤方法
        $this->request->filter(['strip_tags']);

        if ($this->request->isAjax()) {
            //如果发送的来源是Selectpage，则转发到Selectpage
            if ($this->request->request('keyField')) {
                return $this->selectpage();
            }

            //搜索提交过来的数据
            $search_arr = json_decode($this->request->request('filter'),1);
            $where_arr = [];

            if ($search_arr) {
                foreach ($search_arr as $k => $v) {
                    if ($k == 'pay_time'){
                        $create_time = explode(' - ',$v);
                        $where_arr['l.pay_time'] = ['between',[strtotime($create_time[0]),strtotime($create_time[1])]];
                    }else{
                        $key = 'l.'.$k;
                        $where_arr[$key] = $v;
                    }
                    unset($search_arr[$k]);
                }
            }

            $this->request->get(['filter'=>json_encode($search_arr)]);
            list($where, $sort, $order, $offset, $limit) = $this->buildparams();

            $list = Db::name('user_recharge_log')
                ->alias('l')
                ->join(['fa_user u'],'l.user_id = u.id','left')
                ->where($where)
                ->where($where_arr)
                ->where('l.status',1)
                ->field('l.user_id,u.nickname,u.avatar,SUM(COALESCE(l.price, 0)) AS total_amount,count(*) as num')
                ->group('l.user_id')
                ->order('total_amount desc')
                ->paginate($limit);

            $total = $list->total();
            $list = $list->all(); //这是关键

            if ($list) {
                foreach ($list as $k => &$item)
                {
                    $offset++;
                    $item['ranking'] =$offset;
                }
            }
            unset($item);

            $price = Db::name('user_recharge_log')
                ->alias('l')
                ->where($where)
                ->where($where_arr)
                ->where('l.status',1)
                ->sum('l.price');

            $result = array("total" => $total, "rows" => $list,'extend'=>['price'=>$price]);

            return json($result);
        }
        return $this->view->fetch();
    }

    /**
     * 积分排行
     * @Authod Jw
     * @Time 2024/7/23
     * @return string|\think\response\Json
     * @throws Exception
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    public function score()
    {
        //设置过滤方法
        $this->request->filter(['strip_tags']);

        if ($this->request->isAjax()) {
            //如果发送的来源是Selectpage，则转发到Selectpage
            if ($this->request->request('keyField')) {
                return $this->selectpage();
            }

            //搜索提交过来的数据
            $search_arr = json_decode($this->request->request('filter'),1);
            $where_arr = [];

            if ($search_arr) {
                foreach ($search_arr as $k => $v) {
                    if ($k == 'createtime'){
                        $create_time = explode(' - ',$v);
                        $where_arr['l.createtime'] = ['between',[strtotime($create_time[0]),strtotime($create_time[1])]];
                    }else{
                        $key = 'l.'.$k;
                        $where_arr[$key] = $v;
                    }
                    unset($search_arr[$k]);
                }
            }

            $this->request->get(['filter'=>json_encode($search_arr)]);
            list($where, $sort, $order, $offset, $limit) = $this->buildparams();

            $list = Db::name('user_score_log')
                ->alias('l')
                ->join(['fa_user u'],'l.user_id = u.id','left')
                ->where($where)
                ->where($where_arr)
                ->where('l.status',1)
                ->field('l.user_id,u.nickname,u.avatar,SUM(COALESCE(l.score, 0)) AS total_amount,count(*) as num')
                ->group('l.user_id')
                ->order('total_amount desc')
                ->paginate($limit);

            $total = $list->total();
            $list = $list->all(); //这是关键

            if ($list) {
                foreach ($list as $k => &$item)
                {
                    $offset++;
                    $item['ranking'] =$offset;
                }
            }
            unset($item);

            $price = Db::name('user_score_log')
                ->alias('l')
                ->where($where)
                ->where($where_arr)
                ->where('l.status',1)
                ->sum('l.score');

            $result = array("total" => $total, "rows" => $list,'extend'=>['price'=>$price]);

            return json($result);
        }
        return $this->view->fetch();
    }

}
