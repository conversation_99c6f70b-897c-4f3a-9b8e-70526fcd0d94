// 登录弹窗混入
import LoginModal from '@/components/LoginModal.vue'

export default {
  components: {
    LoginModal
  },
  
  data() {
    return {
      showLoginModal: false,
      loginModalMessage: '',
      loginModalCallback: null,
      loginModalLandscape: false
    }
  },
  
  methods: {
    /**
     * 显示登录弹窗
     * @param {string} action 操作名称
     * @param {Function} onConfirm 确认回调
     * @param {Function} onCancel 取消回调
     */
    showLoginConfirmModal(action = '此操作', onConfirm = null, onCancel = null) {
      this.loginModalMessage = `${action}需要登录后才能使用，是否前往登录？`
      this.loginModalCallback = {
        onConfirm: onConfirm,
        onCancel: onCancel
      }
      // 自动检测是否为横屏游戏页面
      this.loginModalLandscape = this.gameInfo && this.gameInfo.is_landscape === 1
      this.showLoginModal = true
    },
    
    /**
     * 关闭登录弹窗
     */
    closeLoginModal() {
      this.showLoginModal = false
      if (this.loginModalCallback && this.loginModalCallback.onCancel) {
        this.loginModalCallback.onCancel()
      }
      this.loginModalCallback = null
    },
    
    /**
     * 确认去登录
     */
    confirmLogin() {
      this.showLoginModal = false
      if (this.loginModalCallback && this.loginModalCallback.onConfirm) {
        this.loginModalCallback.onConfirm()
      } else {
        uni.navigateTo({
          url: '/pages/login/index'
        })
      }
      this.loginModalCallback = null
    }
  }
}