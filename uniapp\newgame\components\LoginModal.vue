<template>
	<UniversalModal
		:show="show"
		title="登录提示"
		:landscape="landscape"
		size="medium"
		@close="handleClose"
	>
		<view class="login-modal-content">
			<text class="login-modal-text">{{ message }}</text>
		</view>
		<template #footer>
			<view class="login-modal-buttons">
				<button class="login-modal-btn cancel" @tap="handleCancel">取消</button>
				<button class="login-modal-btn confirm" @tap="handleConfirm">去登录</button>
			</view>
		</template>
	</UniversalModal>
</template>

<script>
	import UniversalModal from '@/components/UniversalModal.vue'

	export default {
		name: 'LoginModal',
		components: {
			UniversalModal
		},
		props: {
			show: {
				type: Boolean,
				default: false
			},
			message: {
				type: String,
				default: '此操作需要登录后才能使用，是否前往登录？'
			},
			landscape: {
				type: Boolean,
				default: false
			}
		},
		methods: {
			handleClose() {
				this.$emit('close')
			},
			
			handleCancel() {
				this.$emit('cancel')
				this.$emit('close')
			},
			
			handleConfirm() {
				this.$emit('confirm')
				this.$emit('close')
			}
		}
	}
</script>

<style scoped>
	.login-modal-content {
		padding: 20rpx 0;
		text-align: center;
	}

	.login-modal-text {
		color: #fff;
		font-size: 28rpx;
		line-height: 1.6;
	}

	.login-modal-buttons {
		display: flex;
		gap: 20rpx;
		padding: 0 20rpx;
	}

	.login-modal-btn {
		flex: 1;
		height: 70rpx;
		border-radius: 35rpx;
		border: none;
		font-size: 28rpx;
		color: #fff;
		display: flex;
		align-items: center;
		justify-content: center;
	}

	.login-modal-btn.cancel {
		background: #666;
	}

	.login-modal-btn.confirm {
		background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	}
</style>