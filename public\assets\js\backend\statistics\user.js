define(['jquery', 'bootstrap', 'backend', 'table', 'form'], function ($, undefined, Backend, Table, Form) {

    var Controller = {
        index: function () {
            // 初始化表格参数配置
            Table.api.init();

            //绑定事件
            $('a[data-toggle="tab"]').on('shown.bs.tab', function (e) {
                var panel = $($(this).attr("href"));
                if (panel.length > 0) {
                    Controller.table[panel.attr("id")].call(this);
                    $(this).on('click', function (e) {
                        $($(this).attr("href")).find(".btn-refresh").trigger("click");
                    });
                }
                //移除绑定的事件
                $(this).unbind('shown.bs.tab');
            });

            //必须默认触发shown.bs.tab事件
            $('ul.nav-tabs li.active a[data-toggle="tab"]').trigger("shown.bs.tab");
        },
        table: {
            first: function () {
                // 表格1
                var table1 = $("#table1");

                //搜索框自定义
                table1.on('post-common-search.bs.table', function (event, table) {
                    var form = $("form", table.$commonsearch);

                    //商户
                    $("input[name='store_id']", form).addClass("selectpage").data("source", "store/store/select_list").data("primaryKey", "id").data("field", "name").data("pageSize", "15").data("orderBy", "id desc");

                    Form.events.cxselect(form);
                    Form.events.selectpage(form);
                });

                //当表格数据加载完成时
                table1.on('load-success.bs.table', function (e, data) {
                    //这里可以获取从服务端获取的JSON数据

                    //这里我们手动设置底部的值
                    $("#first_price").text(data.extend.price);
                });

                table1.bootstrapTable({
                    url: 'statistics/user/index',
                    extend: {
                        index_url: 'statistics/user/index',
                        add_url: '',
                        edit_url: '',
                        del_url: '',
                        multi_url: '',
                        table: '',
                    },
                    toolbar: '#toolbar1',
                    sortName: 'id',
                    search: false,
                    columns: [
                        [
                            {checkbox: true},
                            {field: 'ranking', title: __('排行'), operate:false},
                            {field: 'avatar', title: __('头像'), events: Table.api.events.image, formatter: Table.api.formatter.image, operate: false},
                            {field: 'user_id', title: __('用户信息'), operate:false,formatter: function (value, row, index) {
                                    if (row.nickname) {
                                        return '昵称：'+row.nickname+'<br>'+'用户ID：'+row.user_id;
                                    }else{
                                        return row.user_id;
                                    }
                                }},
                            {field: 'total_amount',title: __('支付金额(元)'), operate:false},
                            {field: 'num',title: __('订单数量'), operate:false},
                            {field: 'pay_time',title: __('时间'),visible:false, operate:'RANGE', addclass:'datetimerange',defaultValue:Moment().startOf('day').format('YYYY-MM-DD HH:mm:ss') + ' - ' + Moment().endOf('day').format('YYYY-MM-DD HH:mm:ss')},
                            {field: 'operate', title: __('Operate'), table: table1, events: Table.api.events.operate,
                                buttons:[
                                    {
                                        name: 'detail',
                                        text: __('充值记录'),
                                        icon: 'fa fa-list',
                                        extend:'data-area=\'["90%","80%"]\'',
                                        classname: 'btn btn-info btn-xs btn-detail btn-dialog',
                                        url: function (row){
                                            return "recharge/log/index/user_id/" + row.user_id;
                                        },
                                    },
                                ],
                                formatter: Table.api.formatter.operate
                            }

                        ]
                    ]
                });

                // 为表格1绑定事件
                Table.api.bindevent(table1);
            },
            second: function () {
                // 表格2
                var table2 = $("#table2");

                //搜索框自定义
                table2.on('post-common-search.bs.table', function (event, table) {
                    var form = $("form", table.$commonsearch);

                    Form.events.cxselect(form);
                    Form.events.selectpage(form);
                });

                //当表格数据加载完成时
                table2.on('load-success.bs.table', function (e, data) {
                    //这里可以获取从服务端获取的JSON数据

                    //这里我们手动设置底部的值
                    $("#second_price").text(data.extend.price);
                });

                table2.bootstrapTable({
                    url: 'statistics/user/score',
                    toolbar: '#toolbar2',
                    sortName: 'id',
                    search: false,
                    columns: [
                        [
                            {checkbox: true},
                            {field: 'ranking', title: __('排行'), operate:false},
                            {field: 'avatar', title: __('头像'), events: Table.api.events.image, formatter: Table.api.formatter.image, operate: false},
                            {field: 'user_id', title: __('用户信息'), operate:false,formatter: function (value, row, index) {
                                    if (row.nickname) {
                                        return '昵称：'+row.nickname+'<br>'+'用户ID：'+row.user_id;
                                    }else{
                                        return row.user_id;
                                    }
                                }},
                            {field: 'total_amount',title: __('积分'), operate:false},
                            {field: 'createtime',title: __('时间'),visible:false, operate:'RANGE', addclass:'datetimerange',defaultValue:Moment().startOf('day').format('YYYY-MM-DD HH:mm:ss') + ' - ' + Moment().endOf('day').format('YYYY-MM-DD HH:mm:ss')},
                            {field: 'operate', title: __('Operate'), table: table2, events: Table.api.events.operate,
                                buttons:[
                                    {
                                        name: 'detail',
                                        text: __('积分记录'),
                                        icon: 'fa fa-list',
                                        extend:'data-area=\'["90%","80%"]\'',
                                        classname: 'btn btn-info btn-xs btn-detail btn-dialog',
                                        url: function (row){
                                            return "user/score/index/user_id/" + row.user_id;
                                        },
                                    },
                                ],
                                formatter: Table.api.formatter.operate
                            }

                        ]
                    ]
                });

                // 为表格2绑定事件
                Table.api.bindevent(table2);
            },

        },
        add: function () {
            Controller.api.bindevent();
        },
        edit: function () {
            Controller.api.bindevent();
        },
        api: {
            bindevent: function () {
                Form.api.bindevent($("form[role=form]"));
            },
            formatter: {//渲染的方法
                isShow: function () {
                    if (Config.is_show == 0) {//是否显示列
                        return false;
                    }
                },
            },
        }
    };
    return Controller;
});
