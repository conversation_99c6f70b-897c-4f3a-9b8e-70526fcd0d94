// 登录验证和游客模式工具类
import config from '@/config.js'
import modalManager from '@/utils/modalManager.js'

class AuthHelper {
  /**
   * 检查是否已登录
   * @returns {boolean} 是否已登录
   */
  static isLoggedIn() {
    const token = uni.getStorageSync(config.tokenKey)
    return !!token
  }

  /**
   * 获取用户信息
   * @returns {object} 用户信息
   */
  static getUserInfo() {
    const userInfo = uni.getStorageSync(config.userInfo)
    return userInfo || {}
  }

  /**
   * 检查登录状态，如果未登录则显示登录提示
   * @param {string} action 操作名称，用于提示
   * @param {boolean} showModal 是否显示弹窗提示
   * @returns {boolean} 是否已登录
   */
  static checkLogin(action = '此操作', showModal = true) {
    if (!this.isLoggedIn()) {
      if (showModal) {
        modalManager.showLoginModal(action)
      }
      return false
    }
    return true
  }

  /**
   * 静默检查登录状态（不显示提示）
   * @returns {boolean} 是否已登录
   */
  static checkLoginSilent() {
    return this.checkLogin('', false)
  }

  /**
   * 游客模式提示
   * @param {string} action 操作名称
   */
  static showGuestTip(action = '此功能') {
    uni.showToast({
      title: `${action}需要登录后使用`,
      icon: 'none',
      duration: 2000
    })
  }

  /**
   * 清除登录信息
   */
  static logout() {
    uni.removeStorageSync(config.tokenKey)
    uni.removeStorageSync(config.tokenExpireKey)
    uni.removeStorageSync(config.userInfo)
  }

  /**
   * 获取游客模式的默认用户信息
   * @returns {object} 游客默认信息
   */
  static getGuestUserInfo() {
    return {
      nickname: '游客',
      avatar: '/static/avatar.png',
      money: 0,
      score: 0,
      id: 0,
      level_avatar: '/static/level_avatar.png'
    }
  }
}

export default AuthHelper