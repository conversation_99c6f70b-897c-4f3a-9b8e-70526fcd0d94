<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>截图上传接口测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #555;
        }
        input[type="file"], input[type="text"] {
            width: 100%;
            padding: 10px;
            border: 2px solid #ddd;
            border-radius: 5px;
            font-size: 16px;
        }
        button {
            background-color: #007bff;
            color: white;
            padding: 12px 30px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            width: 100%;
        }
        button:hover {
            background-color: #0056b3;
        }
        button:disabled {
            background-color: #ccc;
            cursor: not-allowed;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 5px;
            white-space: pre-wrap;
            font-family: monospace;
        }
        .success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .info {
            background-color: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
        .file-info {
            margin-top: 10px;
            padding: 10px;
            background-color: #f8f9fa;
            border-radius: 5px;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>截图上传接口测试</h1>
        <p><strong>测试域名:</strong> http://testva2.91jdcd.com</p>
        <p><strong>接口地址:</strong> /api/common/upload_screenshot</p>
        
        <form id="uploadForm">
            <div class="form-group">
                <label for="fileInput">选择图片文件:</label>
                <input type="file" id="fileInput" name="file" accept="image/*" required>
                <div id="fileInfo" class="file-info" style="display: none;"></div>
            </div>
            
            <div class="form-group">
                <label for="categoryInput">分类 (可选):</label>
                <input type="text" id="categoryInput" name="category" placeholder="默认为 screenshot" value="screenshot">
            </div>
            
            <button type="submit" id="uploadBtn">上传截图</button>
        </form>
        
        <div id="result" class="result" style="display: none;"></div>
    </div>

    <script>
        const API_BASE_URL = 'http://testva2.91jdcd.com';
        const fileInput = document.getElementById('fileInput');
        const fileInfo = document.getElementById('fileInfo');
        const uploadForm = document.getElementById('uploadForm');
        const uploadBtn = document.getElementById('uploadBtn');
        const result = document.getElementById('result');

        // 文件选择事件
        fileInput.addEventListener('change', function(e) {
            const file = e.target.files[0];
            if (file) {
                const sizeInMB = (file.size / 1024 / 1024).toFixed(2);
                const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/bmp', 'image/webp'];
                const isValidType = allowedTypes.includes(file.type);
                
                fileInfo.innerHTML = `
                    <strong>文件信息:</strong><br>
                    文件名: ${file.name}<br>
                    文件类型: ${file.type}<br>
                    文件大小: ${sizeInMB} MB<br>
                    类型验证: ${isValidType ? '✅ 支持' : '❌ 不支持'}<br>
                    大小验证: ${file.size <= 10485760 ? '✅ 符合要求' : '❌ 超过10MB限制'}
                `;
                fileInfo.style.display = 'block';
                
                // 验证文件
                if (!isValidType) {
                    showResult('文件类型不支持！只允许上传图片文件（jpg, png, gif, bmp, webp）', 'error');
                    return;
                }
                if (file.size > 10485760) {
                    showResult('文件大小超过限制！最大允许10MB', 'error');
                    return;
                }
                
                hideResult();
            } else {
                fileInfo.style.display = 'none';
            }
        });

        // 表单提交事件
        uploadForm.addEventListener('submit', function(e) {
            e.preventDefault();
            
            const file = fileInput.files[0];
            if (!file) {
                showResult('请选择要上传的文件！', 'error');
                return;
            }
            
            uploadFile(file);
        });

        // 上传文件函数
        async function uploadFile(file) {
            const formData = new FormData();
            formData.append('file', file);
            
            const category = document.getElementById('categoryInput').value.trim();
            if (category) {
                formData.append('category', category);
            }
            
            uploadBtn.disabled = true;
            uploadBtn.textContent = '上传中...';
            
            showResult('正在上传文件，请稍候...', 'info');
            
            try {
                const response = await fetch(`${API_BASE_URL}/api/common/upload_screenshot`, {
                    method: 'POST',
                    body: formData
                });
                
                const responseText = await response.text();
                let responseData;
                
                try {
                    responseData = JSON.parse(responseText);
                } catch (e) {
                    throw new Error(`服务器返回非JSON格式数据: ${responseText}`);
                }
                
                if (response.ok) {
                    if (responseData.code === 1) {
                        const resultText = `✅ 上传成功！
                        
响应数据:
${JSON.stringify(responseData, null, 2)}

文件信息:
- 相对路径: ${responseData.data.url}
- 完整URL: ${responseData.data.fullurl}
- 预计文件名格式: ${getCurrentTimeFormat()}_随机字符串.${file.name.split('.').pop()}`;
                        
                        showResult(resultText, 'success');
                    } else {
                        showResult(`❌ 上传失败: ${responseData.msg || '未知错误'}`, 'error');
                    }
                } else {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
            } catch (error) {
                console.error('上传错误:', error);
                showResult(`❌ 上传失败: ${error.message}`, 'error');
            } finally {
                uploadBtn.disabled = false;
                uploadBtn.textContent = '上传截图';
            }
        }

        // 显示结果
        function showResult(message, type) {
            result.textContent = message;
            result.className = `result ${type}`;
            result.style.display = 'block';
        }

        // 隐藏结果
        function hideResult() {
            result.style.display = 'none';
        }

        // 获取当前时间格式（用于预览文件名格式）
        function getCurrentTimeFormat() {
            const now = new Date();
            const year = now.getFullYear();
            const month = String(now.getMonth() + 1).padStart(2, '0');
            const day = String(now.getDate()).padStart(2, '0');
            const hour = String(now.getHours()).padStart(2, '0');
            const minute = String(now.getMinutes()).padStart(2, '0');
            const second = String(now.getSeconds()).padStart(2, '0');
            
            return `${year}${month}${day}${hour}${minute}${second}`;
        }
    </script>
</body>
</html>
