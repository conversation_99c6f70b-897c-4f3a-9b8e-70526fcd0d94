define(['jquery', 'bootstrap', 'backend', 'table', 'form', 'echarts', 'echarts-theme'], function ($, undefined, Backend, Table, Form, Echarts) {

    var Controller = {
        index: function () {
            Form.api.bindevent($("#form1"));

            var refresh_echart = function ( date='') {
                var tishi = layer.msg('加载中,请稍等...', {
                    icon: 16
                    ,shade: 0.3
                    ,time: false
                });

                 setTimeout(function () {
                    Fast.api.ajax({
                        url: 'statistics/index/index',
                        data: {date: date},
                        loading: false
                    }, function (ret) {
                        layer.close(tishi);

                        // 精确匹配带标题的卡片
                        $.each(ret, function(index, item) {
                            var $card = $('.header-conBox[data-name="' + item.title + '"]');
                            if ($card.length) {
                                $card.find('.header-con-msg-num').html(item.value);
                            }
                        });

                        return false;
                    });
                }, 50);
            };

            //点击按钮
            $(document).on("click", ".btn-filter", function () {
                var label = $(this).text();
                var obj = $(this).closest("form").find(".datetimerange").data("daterangepicker");
                var dates = obj.ranges[label];

                obj.startDate = dates[0];
                obj.endDate = dates[1];

                obj.clickApply();

                var date = $(this).closest("form").find(".datetimerange").val();
                refresh_echart(date);
            });

            //点击刷新
            $(document).on("click", ".btn-refresh", function () {
                refresh_echart();
            });

        },
        add: function () {
            Controller.api.bindevent();
        },
        edit: function () {
            Controller.api.bindevent();
        },
        api: {
            bindevent: function () {
                Form.api.bindevent($("form[role=form]"));
            }
        }
    };
    return Controller;
});
