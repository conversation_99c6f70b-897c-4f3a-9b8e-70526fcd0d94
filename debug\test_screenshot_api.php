<?php
/**
 * 截图上传接口测试脚本
 * 测试域名: http://testva2.91jdcd.com
 * 接口地址: /api/common/upload_screenshot
 */

// 测试配置
$apiUrl = 'http://testva2.91jdcd.com/api/common/upload_screenshot';
$testImagePath = __DIR__ . '/test_image.jpg';

echo "=== 截图上传接口测试 ===\n";
echo "测试域名: http://testva2.91jdcd.com\n";
echo "接口地址: /api/common/upload_screenshot\n";
echo "测试时间: " . date('Y-m-d H:i:s') . "\n\n";

// 创建测试图片（如果不存在）
if (!file_exists($testImagePath)) {
    echo "创建测试图片...\n";
    createTestImage($testImagePath);
}

// 测试用例1: 正常上传
echo "=== 测试用例1: 正常上传 ===\n";
testUpload($apiUrl, $testImagePath, 'screenshot');

echo "\n";

// 测试用例2: 自定义分类
echo "=== 测试用例2: 自定义分类 ===\n";
testUpload($apiUrl, $testImagePath, 'test_category');

echo "\n";

// 测试用例3: 不传分类参数
echo "=== 测试用例3: 不传分类参数 ===\n";
testUpload($apiUrl, $testImagePath, null);

/**
 * 测试上传函数
 */
function testUpload($apiUrl, $imagePath, $category = null) {
    if (!file_exists($imagePath)) {
        echo "❌ 测试图片不存在: $imagePath\n";
        return;
    }
    
    $fileInfo = getimagesize($imagePath);
    $fileSize = filesize($imagePath);
    $mimeType = $fileInfo['mime'] ?? 'unknown';
    
    echo "文件信息:\n";
    echo "- 路径: $imagePath\n";
    echo "- 大小: " . formatBytes($fileSize) . "\n";
    echo "- 类型: $mimeType\n";
    echo "- 分类: " . ($category ?: '默认') . "\n\n";
    
    // 准备POST数据
    $postData = [
        'file' => new CURLFile($imagePath, $mimeType, basename($imagePath))
    ];
    
    if ($category !== null) {
        $postData['category'] = $category;
    }
    
    // 发送请求
    $ch = curl_init();
    curl_setopt_array($ch, [
        CURLOPT_URL => $apiUrl,
        CURLOPT_POST => true,
        CURLOPT_POSTFIELDS => $postData,
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_TIMEOUT => 30,
        CURLOPT_FOLLOWLOCATION => true,
        CURLOPT_SSL_VERIFYPEER => false,
        CURLOPT_HTTPHEADER => [
            'User-Agent: Screenshot Upload Test Script'
        ]
    ]);
    
    echo "发送请求...\n";
    $startTime = microtime(true);
    $response = curl_exec($ch);
    $endTime = microtime(true);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    curl_close($ch);
    
    $duration = round(($endTime - $startTime) * 1000, 2);
    
    echo "请求耗时: {$duration}ms\n";
    echo "HTTP状态码: $httpCode\n";
    
    if ($error) {
        echo "❌ CURL错误: $error\n";
        return;
    }
    
    if (!$response) {
        echo "❌ 无响应数据\n";
        return;
    }
    
    echo "响应内容:\n";
    echo $response . "\n\n";
    
    // 解析JSON响应
    $responseData = json_decode($response, true);
    if (json_last_error() !== JSON_ERROR_NONE) {
        echo "❌ JSON解析失败: " . json_last_error_msg() . "\n";
        return;
    }
    
    // 分析响应结果
    if (isset($responseData['code']) && $responseData['code'] == 1) {
        echo "✅ 上传成功!\n";
        if (isset($responseData['data'])) {
            echo "文件URL: " . ($responseData['data']['url'] ?? 'N/A') . "\n";
            echo "完整URL: " . ($responseData['data']['fullurl'] ?? 'N/A') . "\n";
            
            // 分析文件名格式
            if (isset($responseData['data']['url'])) {
                $url = $responseData['data']['url'];
                $filename = basename($url);
                echo "文件名: $filename\n";
                
                // 检查时间格式
                if (preg_match('/(\d{14})_/', $filename, $matches)) {
                    $timeStr = $matches[1];
                    $year = substr($timeStr, 0, 4);
                    $month = substr($timeStr, 4, 2);
                    $day = substr($timeStr, 6, 2);
                    $hour = substr($timeStr, 8, 2);
                    $minute = substr($timeStr, 10, 2);
                    $second = substr($timeStr, 12, 2);
                    
                    echo "✅ 时间格式正确: {$year}-{$month}-{$day} {$hour}:{$minute}:{$second}\n";
                } else {
                    echo "❌ 时间格式不符合预期\n";
                }
            }
        }
    } else {
        echo "❌ 上传失败: " . ($responseData['msg'] ?? '未知错误') . "\n";
    }
}

/**
 * 创建测试图片
 */
function createTestImage($path) {
    $width = 200;
    $height = 150;
    
    $image = imagecreate($width, $height);
    $bgColor = imagecolorallocate($image, 240, 240, 240);
    $textColor = imagecolorallocate($image, 50, 50, 50);
    
    $text = "Test Image\n" . date('Y-m-d H:i:s');
    imagestring($image, 3, 50, 50, $text, $textColor);
    
    imagejpeg($image, $path, 80);
    imagedestroy($image);
    
    echo "✅ 测试图片已创建: $path\n\n";
}

/**
 * 格式化字节大小
 */
function formatBytes($bytes, $precision = 2) {
    $units = array('B', 'KB', 'MB', 'GB', 'TB');
    
    for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
        $bytes /= 1024;
    }
    
    return round($bytes, $precision) . ' ' . $units[$i];
}

echo "\n=== 测试完成 ===\n";
echo "注意事项:\n";
echo "1. 确保域名 http://testva2.91jdcd.com 可访问\n";
echo "2. 检查服务器是否正常运行\n";
echo "3. 验证文件名是否包含精确到秒的时间戳\n";
echo "4. 确认文件保存路径格式: /screenshot/年/月/年月日时分秒_随机字符串.扩展名\n";
?>
